# 本番環境デプロイメントガイド

## 概要

このドキュメントは、`docker-compose.prod.yml` を使用して中古車販売プラットフォームを本番環境にデプロイする方法を説明します。

## 前提条件

- Docker と Docker Compose がインストールされていること
- 本番用の `.env.prod` ファイルが適切に設定されていること
- GitHub Container Registry への適切なアクセス権限

## デプロイ手順

### 1. 環境ファイルの準備

本番環境用の `.env.prod` ファイルを作成し、適切な値を設定してください：

```bash
cp .env.example .env.prod
```

`.env.prod` ファイルを編集して本番環境に適した値を設定します。

**重要な環境変数:**
```env
# Directus 設定（本番環境では必ず変更してください）
DIRECTUS_KEY=replace-with-secure-32-character-key
DIRECTUS_SECRET=replace-with-secure-64-character-secret
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=secure-password-here
DIRECTUS_PUBLIC_URL=https://your-domain.com:8056
DIRECTUS_APP_TOKEN=your-server-side-api-token-here

# その他の重要な設定
EXTERNAL_HOST=your-server-ip-or-domain
NODE_ENV=production
```

### 2. 本番環境でのコンテナ起動

```bash
# 本番用の Docker Compose を使用してサービスを開始
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# またはサービスを個別に起動する場合
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d cockroach_db
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d redis
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d directus
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d nuxt-app
```

### 3. サービスの状態確認

```bash
# コンテナの状態を確認
docker-compose -f docker-compose.prod.yml ps

# ログを確認
docker-compose -f docker-compose.prod.yml logs -f

# 特定のサービスのログを確認
docker-compose -f docker-compose.prod.yml logs -f nuxt-app
docker-compose -f docker-compose.prod.yml logs -f directus
```

### 4. データベース初期化の確認

```bash
# データベース初期化が完了したかを確認
docker-compose -f docker-compose.prod.yml logs db-init
```

## サービス詳細

### Nuxt アプリケーション
- **コンテナ名**: `usedcar_nuxt_app`
- **ポート**: `${APP_PORT:-3001}:3000`
- **イメージ**: `ghcr.io/cis-gk-dev/usedcarsales-nuxt:dokploy`

### CockroachDB データベース
- **コンテナ名**: `usedcar_cockroachdb`
- **データベースポート**: `${DB_EXTERNAL_PORT:-26258}:26257`
- **データ永続化**: `cockroach_data` ボリューム

### Directus 管理インターフェース
- **コンテナ名**: `usedcar_directus`
- **ポート**: `${DIRECTUS_PORT:-8056}:8055`
- **機能**: REST API、データ管理、CORS 対応

### Redis キャッシュ
- **コンテナ名**: `${COMPOSE_PROJECT_NAME:-usedcar}_redis`
- **ポート**: `${REDIS_PORT:-6379}:6379`
- **用途**: Directus のパフォーマンス向上

## アクセス方法

デプロイ完了後、以下の URL でサービスにアクセスできます：

- **メインアプリケーション**: `http://your-server-ip:3001`
- **Directus 管理画面**: `http://your-server-ip:8056`

## 停止とクリーンアップ

```bash
# サービスを停止（データは保持）
docker-compose -f docker-compose.prod.yml --env-file .env.prod down

# サービスを停止してボリュームも削除
docker-compose -f docker-compose.prod.yml --env-file .env.prod down -v

# 使用していないイメージも削除
docker system prune -a
```

## トラブルシューティング

### よくある問題

1. **コンテナが起動しない**
   ```bash
   # ログを確認
   docker-compose -f docker-compose.prod.yml logs [サービス名]
   ```

2. **データベース接続エラー**
   - `.env.prod` でデータベース設定を確認
   - CockroachDB コンテナが起動しているか確認

3. **ポート衝突**
   - `.env.prod` で異なるポート番号を指定
   - 既存のサービスとの衝突を確認

### ヘルスチェック

```bash
# Redis の健康状態を確認
docker exec ${COMPOSE_PROJECT_NAME:-usedcar}_redis redis-cli ping

# Nuxt アプリケーションの状態を確認
curl -f http://localhost:3001
```

## セキュリティ注意事項

- **本番環境では必ず強力なパスワードを使用してください**
- **`DIRECTUS_APP_TOKEN`**: Directus管理画面でサーバーサイドAPI用の静的トークンを生成し設定してください
- **ファイアウォール設定**で必要なポートのみを開放してください
- **定期的にバックアップ**を取得してください
- **SSL/TLS証明書**を適切に設定してください
- **APIトークンの管理**: 定期的にトークンをローテーションしてください

## バックアップ

```bash
# データベースのバックアップ
docker exec usedcar_cockroachdb cockroach dump usedcarsales --insecure > backup.sql

# Redis のバックアップ
docker exec ${COMPOSE_PROJECT_NAME:-usedcar}_redis redis-cli BGSAVE
```

## モニタリング

定期的に以下をモニタリングしてください：

- サービスの稼働状況
- リソース使用量（CPU、メモリ、ディスク）
- ログファイルのサイズとエラー
- データベースのパフォーマンス

## サポート

問題が発生した場合は、システム管理者に連絡するか、プロジェクトのドキュメントを参照してください。
services:
  cockroach_db:
    image: cockroachdb/cockroach:latest-v24.1
    container_name: usedcar_cockroachdb
    ports:
      - "26257:26257"
      - "${DB_UI_PORT:-8081}:8080"
    command: start-single-node --insecure
    volumes:
      - cockroach_data:/cockroach/cockroach-data
    environment:
      - COCKROACH_DATABASE=usedcarsales
      - COCKROACH_USER=root
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health?ready=1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - usedcar_network

  db-init:
    image: postgres:15-alpine
    container_name: usedcar_db_init
    volumes:
      - ./scraper/database/init:/init-scripts
    environment:
      - PGPASSWORD=""
    command: >
      sh -c "
        echo 'Waiting for CockroachDB to be ready...' &&
        sleep 15 &&
        echo 'Running complete database initialization...' &&
        psql -h cockroach_db -p 26257 -U root -d postgres -f /init-scripts/01-init.sql &&
        echo 'Database initialization completed successfully'
      "
    depends_on:
      cockroach_db:
        condition: service_healthy
    networks:
      - usedcar_network
    restart: "no"

  directus:
    image: directus/directus:latest
    container_name: usedcar_directus
    ports:
      - "${DIRECTUS_PORT:-8056}:8055"
    volumes:
      - ./directus/uploads:/directus/uploads
      - ./directus/extensions:/directus/extensions
      - ./directus/database:/directus/database
    env_file:
      - .env
    environment:
      # Database Configuration (using shared ENV variables)
      DB_CLIENT: cockroachdb
      DB_HOST: cockroach_db
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL: ${DB_SSL}

      # Directus Configuration from ENV variables
      KEY: ${DIRECTUS_KEY}
      SECRET: ${DIRECTUS_SECRET}

      # Admin User from ENV variables
      ADMIN_EMAIL: ${DIRECTUS_ADMIN_EMAIL}
      ADMIN_PASSWORD: ${DIRECTUS_ADMIN_PASSWORD}

      # Public URL from ENV variables
      PUBLIC_URL: ${DIRECTUS_PUBLIC_URL}
 
      # CORS Configuration for client-side access (Bubble.io)
      CORS_ENABLED: true
      CORS_ORIGIN: "http://localhost:3000,http://************:3001"
      CORS_METHODS: "GET,POST,PATCH,DELETE,OPTIONS"
      CORS_ALLOWED_HEADERS: "Content-Type,Authorization"


      # Force fresh installation
      RESET_ADMIN: true
    depends_on:
      db-init:
        condition: service_completed_successfully
    networks:
      - usedcar_network
    restart: unless-stopped

volumes:
  cockroach_data:
    driver: local

networks:
  usedcar_network:
    driver: bridge

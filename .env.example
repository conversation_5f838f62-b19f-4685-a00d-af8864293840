# Environment Configuration for Used Car Sales Platform
# Copy this file to .env and modify as needed

# Environment: development, production
NODE_ENV=production

# External Access Configuration
EXTERNAL_HOST=localhost        # Server IP/domain (change to your server IP or domain)
APP_PORT=3001                  # Main application port
DIRECTUS_PORT=8056             # Directus admin interface port  
DB_EXTERNAL_PORT=26258         # CockroachDB external connection port
DB_UI_PORT=8081                # CockroachDB web UI port

# Database Configuration
# For local development: localhost
# For Docker: cockroach_db (container name)
DB_HOST=cockroach_db
DB_PORT=26257
DB_NAME=usedcarsales
DB_USER=root
DB_PASSWORD=
DB_SSL=false
DB_POOL_MAX=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000

# Scraper Configuration
OUTPUT_MODE=database
BATCH_SIZE=100
CSV_OUTPUT_DIR=./scraper/output
CSV_MERGE_OUTPUT_DIR=./scraper/output_merge

# Blacklist Configuration
BLACKLIST_ENABLED=true
BLACKLIST_CACHE_EXPIRY=300000
BLACKLIST_FAIL_OPEN=true

# Scraper Scheduling
SCRAPER_SCHEDULE_ENABLED=false
SCRAPER_SCHEDULE_CRON=0 0 2 * * 0

# Nuxt Configuration
NUXT_DEVTOOLS_ENABLED=true

# Directus Configuration
# SECURITY WARNING: Change these default values for production!
# Generate secure random keys: openssl rand -hex 32
DIRECTUS_KEY=replace-with-secure-32-character-key
DIRECTUS_SECRET=replace-with-secure-64-character-secret
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=admin123
DIRECTUS_PUBLIC_URL=http://localhost:8056
DIRECTUS_APP_TOKEN=your-server-side-api-token-here

# Directus Cache Configuration  
# Note: Cache settings are configured directly in docker-compose files
# These environment variables are no longer needed as we use hardcoded values
<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar title="新規車両登録">
        <template #leading>
        </template>
        <template #trailing>
          <UButton
            @click="confirmCancel"
            variant="ghost"
            icon="i-heroicons-arrow-left"
          >
            戻る
          </UButton>
        </template>
        <template #right>
          <!-- Reset -->
          <UButton
            @click="resetForm"
            block
            variant="outline"
            :disabled="!hasUnsavedChanges"
            leading-icon="i-heroicons-arrow-path"
            class="px-4 w-max"
            >リセット
          </UButton>
          <DevOnly>
            <UButton
              @click="fillMockData"
              block
              variant="soft"
              leading-icon="i-heroicons-beaker"
              class="px-4 w-max"
            >
              Dev Fillup
            </UButton>
          </DevOnly>
          <UButton
            @click="onSubmit"
            block
            :loading="submitting"
            :disabled="submitting || !hasUnsavedChanges || hasInvalidImages"
            leading-icon="i-heroicons-check"
            class="ml-2 px-8 w-max"
          >
            保存
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <!-- Main Content -->
      <div class="max-w-5xl mx-auto p-6">
        <div class="flex items-center justify-between mb-6">
          <h1 class="text-2xl font-bold">新しい車両を追加</h1>
          <div class="flex gap-2">
            <UBadge v-if="hasInvalidImages" color="error" variant="soft">
              無効な画像URLがあります
            </UBadge>
            <UBadge v-if="hasUnsavedChanges" color="warning" variant="soft">
              未保存の変更があります
            </UBadge>
          </div>
        </div>

        <UForm
          :schema="schema"
          :state="state"
          class="grid grid-cols-1 gap-6 lg:grid-cols-2"
          @submit="onSubmit"
        >
          <div class="grid gap-6">
            <!-- Basic Information -->
            <UCard id="basic-info" class="">
              <template #header>
                <h2 class="text-lg font-semibold flex items-center">
                  <UIcon
                    name="i-heroicons-information-circle"
                    class="mr-2 h-5 w-5 text-primary"
                  />
                  基本情報
                </h2>
              </template>

              <div
                class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
              >
                <UFormField
                  label="車名"
                  name="car_name"
                  class="md:col-span-2 xl:col-span-"
                >
                  <UInput
                    v-model="state.car_name"
                    placeholder="車名を入力"
                    class="w-full"
                  />
                </UFormField>

                <UFormField label="メーカー" name="maker">
                  <UInput
                    v-model="state.maker"
                    placeholder="メーカーを入力"
                  />
                </UFormField>

                <UFormField label="モデル" name="model">
                  <UInput v-model="state.model" placeholder="モデルを入力" />
                </UFormField>

                <UFormField label="年式" name="model_year">
                  <UInput v-model="state.model_year" placeholder="例: 2020" />
                </UFormField>

                <UFormField label="価格 (万円)" name="price">
                  <UInputNumber
                    v-model.number="state.price"
                    placeholder="例: 150"
                  />
                </UFormField>

                <UFormField
                  label="走行距離 (km)"
                  name="mileage"
                  class="col-span-2"
                >
                  <UInputNumber
                    v-model.number="state.mileage"
                    placeholder="例: 50000"
                    class="w-full"
                  />
                </UFormField>
              </div>
            </UCard>

            <!-- Technical Specifications -->
            <UCard id="technical-specs" class="">
              <template #header>
                <h2 class="text-lg font-semibold flex items-center">
                  <UIcon
                    name="i-heroicons-cog-6-tooth"
                    class="mr-2 h-5 w-5 text-primary"
                  />
                  仕様
                </h2>
              </template>

              <div
                class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
              >
                <UFormField label="ミッション" name="mission">
                  <UInput
                    v-model="state.mission"
                    placeholder="ミッションを選択"
                  />
                </UFormField>

                <UFormField label="駆動方式" name="driving_system">
                  <UInput
                    v-model="state.driving_system"
                    placeholder="駆動方式を選択"
                  />
                </UFormField>

                <UFormField label="エンジン種類" name="engine_type">
                  <UInput
                    v-model="state.engine_type"
                    placeholder="エンジン種類を選択"
                  />
                </UFormField>

                <UFormField label="ハンドル" name="handle">
                  <UInput
                    v-model="state.handle"
                    placeholder="ハンドルを選択"
                  />
                </UFormField>

                <UFormField label="排気量 (cc)" name="displacement">
                  <UInputNumber
                    v-model.number="state.displacement"
                    placeholder="例: 2000"
                  />
                </UFormField>

                <UFormField label="修復歴" name="fix_history">
                  <UInput
                    v-model="state.fix_history"
                    placeholder="修復歴を選択"
                  />
                </UFormField>

                <UFormField label="車検" name="vehicle_inspection">
                  <UInput
                    v-model="state.vehicle_inspection"
                    placeholder="例: 2025年3月"
                  />
                </UFormField>

                <UFormField label="保険" name="insurance">
                  <UInput v-model="state.insurance" placeholder="保険情報" />
                </UFormField>

                <UFormField label="整備" name="maintenance">
                  <UInput
                    v-model="state.maintenance"
                    placeholder="整備情報"
                  />
                </UFormField>
              </div>
            </UCard>
          </div>
          <!-- Additional Information -->
          <UCard id="additional-info">
            <template #header>
              <h2 class="text-lg font-semibold flex items-center">
                <UIcon
                  name="i-heroicons-document-text"
                  class="mr-2 h-5 w-5 text-primary"
                />
                追加情報
              </h2>
            </template>

            <div class="space-y-6">
              <UFormField label="詳細" name="detail">
                <UTextarea
                  v-model="state.detail"
                  placeholder="車両の詳細情報を入力"
                  :rows="4"
                  autoresize
                  class="w-full"
                />
              </UFormField>

              <UFormField label="画像URL" name="image">
                <div class="space-y-3">
                  <div
                    v-for="(image, index) in imageList"
                    :key="image.id"
                    class="flex items-center gap-1"
                  >
                    <!-- Thumbnail -->
                    <div class="flex-shrink-0 w-16 h-16">
                      <img
                        v-if="image.url && image.isValid === true"
                        :src="image.url"
                        :alt="`Image ${index + 1}`"
                        class="w-full h-full object-cover border border-green-200 rounded"
                        @error="(e) => onImageError(e, index)"
                        @load="(e) => onImageLoad(e, index)"
                      />
                      <div
                        v-else-if="image.isValidating"
                        class="w-full h-full flex items-center justify-center text-blue-400 border border-blue-200 rounded bg-blue-50 animate-pulse"
                      >
                        <UIcon name="i-heroicons-arrow-path" class="h-6 w-6 animate-spin" />
                      </div>
                      <div
                        v-else-if="image.url && image.isValid === false"
                        class="w-full h-full flex items-center justify-center text-red-400 border border-red-200 rounded bg-red-50"
                        :title="`画像の読み込みに失敗しました: ${image.url}`"
                      >
                        <UIcon name="i-heroicons-x-mark" class="h-6 w-6" />
                      </div>
                      <div
                        v-else
                        class="w-full h-full flex items-center justify-center text-gray-400 border border-gray-200 rounded"
                      >
                        <UIcon name="i-heroicons-photo" class="h-6 w-6" />
                      </div>
                    </div>

                    <!-- URL Input -->
                    <div class="flex-1">
                      <UInput
                        v-model="image.url"
                        placeholder="画像URLを入力"
                        @input="handleImageUrlInput(index)"
                        @paste="(e) => handleImageUrlPaste(e, index)"
                        size="sm"
                        class="w-full"
                        :class="{
                          'border-green-300': image.isValid === true,
                          'border-red-300': image.isValid === false,
                          'border-blue-300': image.isValidating
                        }"
                      />
                    </div>

                    <!-- Delete Button -->
                    <UButton
                      v-if="imageList.length > 1"
                      @click="removeImage(index)"
                      variant="ghost"
                      color="error"
                      size="sm"
                      icon="i-heroicons-x-mark"
                      class="flex-shrink-0"
                    />
                  </div>

                  <!-- Add Image Button -->
                  <UButton
                    @click="addImage"
                    variant="outline"
                    size="sm"
                    icon="i-heroicons-plus"
                    class="w-full"
                  >
                    画像を追加
                  </UButton>
                </div>
              </UFormField>

              <UFormField label="取得元URL" name="source_url">
                <UInput
                  v-model="state.source_url"
                  placeholder="https://..."
                  class="w-full"
                />
              </UFormField>

              <UFormField label="取得元" name="source">
                <UInput
                  v-model="state.source"
                  placeholder="取得元サイト名"
                  class="w-full"
                />
              </UFormField>
            </div>
          </UCard>

          <!-- Form Actions - Desktop Optimized -->
          <div class="xl:hidden">
            <!-- Mobile/Tablet Actions -->
            <div class="flex justify-between items-center pt-6 border-t">
              <div class="flex gap-2">
                <UButton @click="confirmCancel" variant="outline" size="sm">
                  キャンセル
                </UButton>
                <UButton
                  @click="resetForm"
                  variant="outline"
                  size="sm"
                  :disabled="!hasUnsavedChanges"
                >
                  <UIcon name="i-heroicons-arrow-path" class="mr-1 h-3 w-3" />
                  リセット
                </UButton>
              </div>

              <UButton
                type="submit"
                :loading="submitting"
                :disabled="submitting || hasInvalidImages"
                color="primary"
                size="sm"
              >
                <UIcon name="i-heroicons-check" class="mr-1 h-3 w-3" />
                車両を追加
              </UButton>
            </div>
          </div>

          <!-- Desktop Sticky Actions -->
          <div class="hidden xl:block lg:col-span-2">
            <div
              class="sticky bottom-0 bg-white border-t border-gray-200 p-4 -mx-6"
            >
              <div
                class="flex justify-between items-center max-w-5xl mx-auto px-6"
              >
                <div class="flex items-center gap-4">
                  <UButton @click="confirmCancel" variant="outline">
                    <UIcon
                      name="i-heroicons-arrow-left"
                      class="mr-2 h-4 w-4"
                    />
                    キャンセル
                  </UButton>
                  <div class="text-sm text-gray-500">
                    <span v-if="hasInvalidImages" class="text-red-600">
                      <UIcon
                        name="i-heroicons-exclamation-triangle"
                        class="inline h-4 w-4 mr-1"
                      />
                      無効な画像URLがあります
                    </span>
                    <span v-else-if="hasUnsavedChanges" class="text-orange-600">
                      <UIcon
                        name="i-heroicons-exclamation-triangle"
                        class="inline h-4 w-4 mr-1"
                      />
                      未保存の変更があります
                    </span>
                    <span v-else class="text-green-600">
                      <UIcon
                        name="i-heroicons-check-circle"
                        class="inline h-4 w-4 mr-1"
                      />
                      入力待ち
                    </span>
                  </div>
                </div>

                <div class="flex gap-3">
                  <UButton
                    @click="resetForm"
                    variant="outline"
                    :disabled="!hasUnsavedChanges"
                  >
                    <UIcon
                      name="i-heroicons-arrow-path"
                      class="mr-2 h-4 w-4"
                    />
                    リセット
                  </UButton>
                  <DevOnly>
                    <UButton
                      @click="fillMockData"
                      variant="soft"
                    >
                      <UIcon
                        name="i-heroicons-beaker"
                        class="mr-2 h-4 w-4"
                      />
                      Dev Fillup
                    </UButton>
                  </DevOnly>
                  <UButton
                    type="submit"
                    :loading="submitting"
                    :disabled="submitting || hasInvalidImages"
                    color="primary"
                    size="lg"
                  >
                    <UIcon name="i-heroicons-check" class="mr-2 h-5 w-5" />
                    車両を追加
                  </UButton>
                </div>
              </div>
            </div>
          </div>
        </UForm>
      </div>
    </template>
  </UDashboardPanel>
</template>

<script lang="ts" setup>
import { z } from "zod";

const config = useRuntimeConfig();

// Validation schema using Zod
const schema = z.object({
  car_name: z.string().min(1, "車名を入力してください"),
  maker: z.string().min(1, "メーカーを入力してください"),
  model: z.string().min(1, "モデルを入力してください"),
  model_year: z.string().min(1, "年式を入力してください"),
  price: z.number().min(0, "価格は0以上の数値を入力してください"),
  mileage: z.number().min(0, "走行距離は0以上の数値を入力してください"),
  mission: z.string().min(1, "ミッションを入力してください"),
  driving_system: z.string().min(1, "駆動方式を入力してください"),
  engine_type: z.string().min(1, "エンジン種類を入力してください"),
  handle: z.string().min(1, "ハンドルを入力してください"),
  displacement: z.number().min(1, "排気量は1以上の数値を入力してください"),
  fix_history: z.string().min(1, "修復歴を入力してください"),
  vehicle_inspection: z.string().optional(),
  insurance: z.string().optional(),
  maintenance: z.string().optional(),
  detail: z.string().optional(),
  image: z.string().optional(),
  source_url: z
    .union([z.string().url("正しいURLを入力してください"), z.literal("")])
    .optional(),
  source: z.string().optional(),
});

// Form state with helpful defaults
const state = reactive({
  car_name: "",
  maker: "",
  model: "",
  model_year: new Date().getFullYear().toString(), // Current year as default
  price: 0,
  mileage: 0,
  mission: "",
  driving_system: "",
  engine_type: "",
  handle: "右", // Default to right-hand drive (common in Japan)
  displacement: 0,
  fix_history: "なし", // Default to "no repair history"
  vehicle_inspection: "",
  insurance: "",
  maintenance: "",
  detail: "",
  image: "",
  source_url: "",
  source: "",
});

// Image list for simplified management
const imageList = ref<Array<{ id: string; url: string; failed?: boolean; isValidating?: boolean; isValid?: boolean }>>([
  { id: `img-${Date.now()}`, url: "" }
]);

// Add new image row
function addImage() {
  imageList.value.push({
    id: `img-${Date.now()}`,
    url: "",
    failed: false,
    isValid: undefined,
  });
}

// Remove image row
function removeImage(index: number) {
  imageList.value.splice(index, 1);
  updateImageCsv();
}

// Update CSV string from imageList
function updateImageCsv() {
  const urls = imageList.value
    .filter((img) => img.url.trim().length > 0 && img.isValid === true)
    .map((img) => img.url.trim());
  state.image = urls.join(", ");
}

// Validate image URL with debouncing
let debounceTimer: NodeJS.Timeout | null = null;
function validateImageDebounced(index: number) {
  if (debounceTimer) clearTimeout(debounceTimer);
  
  debounceTimer = setTimeout(() => {
    const image = imageList.value[index];
    if (!image || !image.url.trim()) {
      if (image) {
        image.isValid = undefined;
        image.isValidating = false;
      }
      return;
    }
    
    image.isValidating = true;
    const isValid = validateImageUrl(image.url.trim());
    image.isValid = isValid;
    image.isValidating = false;
    
    // Update CSV after validation
    updateImageCsv();
  }, 500); // Reduced debounce time since no async call
}

// Handle image URL input
function handleImageUrlInput(index: number) {
  const image = imageList.value[index];
  if (!image) return;
  
  // Reset validation state
  image.isValid = undefined;
  image.isValidating = false;
  image.failed = false;
  
  // Update CSV immediately (but only with valid URLs)
  updateImageCsv();
  
  // Start validation if URL is not empty
  if (image.url.trim()) {
    validateImageDebounced(index);
  }
}

// Handle image URL paste specifically
function handleImageUrlPaste(event: ClipboardEvent, index: number) {
  // Use setTimeout to ensure the pasted value is available in the input
  setTimeout(() => {
    handleImageUrlInput(index);
  }, 10);
}

// Form submission state
const submitting = ref(false);

// Track original values for unsaved changes detection
const originalState = {
  car_name: "",
  maker: "",
  model: "",
  model_year: "",
  price: null,
  mileage: null,
  mission: "",
  driving_system: "",
  engine_type: "",
  handle: "",
  displacement: null,
  fix_history: "",
  vehicle_inspection: "",
  insurance: "",
  maintenance: "",
  detail: "",
  image: "",
  source_url: "",
  source: "",
};

// Computed properties
const hasUnsavedChanges = computed(() => {
  return JSON.stringify(state) !== JSON.stringify(originalState);
});

// Check if there are any invalid image URLs
const hasInvalidImages = computed(() => {
  return imageList.value.some(img => 
    img.url.trim().length > 0 && (img.isValid === false || img.isValidating)
  );
});

// Function to validate image URLs using file extension
function validateImageUrl(url: string): boolean {
  if (!url || typeof url !== "string") return false;
  url = url.trim();
  if (!url) return false;

  // Check if it's a valid URL format
  const urlPattern = /^(https?:\/\/|\/\/)[^\s<>"{}|\\^`[\]]+$/i;
  if (!urlPattern.test(url)) return false;

  // Check for image file extensions
  const imageExtensions = /\.(jpg|jpeg|png|gif|webp|bmp|svg|tiff|tif|ico)(\?.*)?$/i;
  return imageExtensions.test(url);
}

function onImageError(_event: Event, index: number) {
  // Mark this image as failed
  if (imageList.value[index]) {
    imageList.value[index].failed = true;
  }
}

function onImageLoad(_event: Event, index: number) {
  // Mark this image as successfully loaded
  if (imageList.value[index]) {
    imageList.value[index].failed = false;
  }
}

// Mock data for development
function fillMockData() {
  Object.assign(state, {
    car_name: "トヨタ プリウス ハイブリッド",
    maker: "トヨタ",
    model: "プリウス",
    model_year: "2004",
    price: 280,
    mileage: 15000,
    mission: "CVT",
    driving_system: "2WD",
    engine_type: "ハイブリッド",
    handle: "右",
    displacement: 1800,
    fix_history: "なし",
    vehicle_inspection: "2024年3月",
    insurance: "自賠責・任意保険込み",
    maintenance: "定期点検済み",
    detail: "低走行距離で非常に状態の良い車両です。前オーナーは高齢者の方で、主に近所の買い物にのみ使用されていました。内外装ともに綺麗で、メンテナンスもディーラーで定期的に行われていました。",
    image: "https://placehold.co/600.jpg, https://placehold.co/600.jpg, https://placehold.co/600.jpg",
    source_url: "",
    source: "",
  });
  
  // Update image list
  const mockImages = [
    "https://placehold.co/600.jpg",
    "https://placehold.co/600.jpg", 
    "https://placehold.co/600.jpg"
  ];
  
  imageList.value = mockImages.map((url, index) => ({
    id: `img-${Date.now()}-${index}`,
    url
  }));
}

// Form submit handler
async function onSubmit() {
  submitting.value = true;

  try {
    const { dFetch } = useDirectus();

    const response = await dFetch("/items/used_cars", {
      method: "POST",
      body: {
        ...state,
        // Handle numeric fields properly
        price: typeof state.price === "number" ? state.price : null,
        mileage: typeof state.mileage === "number" ? state.mileage : null,
        displacement:
          typeof state.displacement === "number" ? state.displacement : null,
        // Handle date fields properly - send null for empty strings
        model_year: state.model_year?.trim() || null,
        // Set scraped_at to current timestamp for manually added cars
        scraped_at: new Date().toISOString(),
      },
    }) as { id?: string };

    // Show success toast
    const toast = useToast();
    toast.add({
      title: "追加完了",
      description: "新しい車両を追加しました",
      color: "success",
    });

    // Set source_url and source for tracking if not provided
    if (response && response.id && !state.source_url?.trim()) {
      try {
        await dFetch(`/items/used_cars/${response.id}`, {
          method: "PATCH",
          body: { 
            source_url: `${config.public.directus.url}/items/used_cars/${response.id}`,
            source: "manual"
          }
        });
      } catch (updateError) {
        console.warn("Failed to set tracking source_url and source:", updateError);
        // Continue anyway, this is not critical
      }
    }

    // Invalidate deduplicated cars cache by making a request with invalidate=true
    try {
      await $fetch('/api/cars/deduplicated?invalidate=true')
    } catch (cacheError) {
      console.warn('Failed to invalidate cache:', cacheError)
    }

    // Navigate to the new car's detail page
    if (response && response.id) {
      await navigateTo({ name: "cars-id", params: { id: response.id } });
    } else {
      // If no ID returned, go to cars list
      await navigateTo("/cars");
    }
  } catch (error) {
    console.error("Failed to create car:", error);

    const toast = useToast();
    toast.add({
      title: "追加エラー",
      description: "車両の追加に失敗しました",
      color: "error",
    });
  } finally {
    submitting.value = false;
  }
}

// Form management functions
function resetForm() {
  Object.assign(state, {
    car_name: "",
    maker: "",
    model: "",
    model_year: "",
    price: null,
    mileage: null,
    mission: "",
    driving_system: "",
    engine_type: "",
    handle: "",
    displacement: null,
    fix_history: "",
    vehicle_inspection: "",
    insurance: "",
    maintenance: "",
    detail: "",
    image: "",
    source_url: "",
    source: "",
  });
  
  // Reset image list
  imageList.value = [{ id: `img-${Date.now()}`, url: "" }];
}

async function confirmCancel() {
  if (hasUnsavedChanges.value) {
    const confirmed = confirm(
      "未保存の変更があります。変更内容が失われますが、よろしいですか？"
    );
    if (confirmed) {
      await navigateTo("/cars");
    }
  } else {
    await navigateTo("/cars");
  }
}
</script>
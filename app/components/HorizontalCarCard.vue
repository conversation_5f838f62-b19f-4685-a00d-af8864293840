<template>
  <div
    class="p-3 flex flex-col md:flex-row gap-3 outline-1 outline-gray-200 shadow hover:outline-gray-300 hover:shadow-lg transition-all rounded-lg justify-start"
  >
    <div
      id="image-carousel"
      class="flex flex-col w-full md:flex-shrink-0 md:self-start md:w-auto"
    >
      <UCarousel
        ref="carousel"
        v-slot="{ item }"
        wheel-gestures
        :items="images"
        :prev="{ onClick: onClickPrev }"
        :next="{ onClick: onClickNext }"
        class="w-full md:max-w-xs mx-auto grow"
        @select="onSelect"
      >
        <img
          :src="item"
          class="rounded cursor-pointer"
          loading="lazy"
          @click="navigateTo({ name: 'cars-id', params: { id: car.id } })"
        />
      </UCarousel>

      <div
        class="flex gap-1 md:justify-start pt-2 w-full md:max-w-xs justify-center"
      >
        <div
          v-for="(item, index) in images"
          :key="index"
          class="opacity-50 hover:opacity-100 transition-opacity"
          :class="{ 'opacity-100': activeIndex === index }"
          @click="select(index)"
        >
          <img
            :src="item"
            class="max-h-8 aspect-square object-cover rounded cursor-pointer"
          />
        </div>
      </div>
    </div>

    <div id="car-info" class="flex flex-col gap-2 w-full">
      <nuxt-link
        :to="{ name: 'cars-id', params: { id: car.id } }"
        id="car_name"
        class="text-xl text-primary-600 font-bold"
      >
        <!-- truncate if too long -->
        {{ car.car_name.slice(0, 40) }}
      </nuxt-link>
      <div id="details-section" class="flex flex-col h-full">
        <div class="grid gap-4 grid-cols-12 flex-grow">
          <div class="col-span-4 md:col-span-2 xl:col-span-2 h-full flex flex-col min-w-0">
            <p class="font-semibold text-xs md:text-sm whitespace-nowrap">車体本体価格</p>
            <p v-if="car.price" class="text-lg md:text-2xl xl:text-3xl text-error font-bold leading-tight break-words">
              <template v-if="car.price === 0 || (car.price as any) === '0.00'">
                応談
              </template>
              <template v-else>
                <span class="whitespace-nowrap">{{ formatNumber(car.price) }}</span>
                <span class="text-xs md:text-sm text-black whitespace-nowrap">万円</span>
              </template>
            </p>

            <UButton
              :to="car.source_url ?? '#'"
              variant="ghost"
              external
              target="_blank"
              class="mt-auto text-dimmed text-xs line-clamp-2 break-all w-max"
              trailing-icon="i-heroicons-arrow-top-right-on-square"
              :ui="{
                trailingIcon: 'translate-y-0.5 w-3 h-3',
              }"
              >取得元
            </UButton>

            <!-- メタデータ: 作成日・更新日 -->
            <div class="mt-2 space-y-1 text-gray-600">
              <div v-if="car?.created_at">
                <div class="text-xs">作成日:</div>
                <div class="text-xs">{{ formatDate(car.created_at) }}</div>
              </div>
              <div v-if="car?.scraped_at">
                <div class="text-xs">更新日:</div>
                <div class="text-xs">{{ formatDate(car.scraped_at) }}</div>
              </div>
            </div>
            
          </div>
          <div
            id="detail-item-wrapper"
            class="grid grid-cols-1 md:grid-cols-2 col-span-8 md:col-span-10"
          >
            <div class="flex flex-col gap-2 text-xs font-bold">
              <DetailItem
                title="年式"
                :value="String(car.model_year ?? '-').slice(0, 4)"
              />
              <DetailItem title="車検" :value="displayValue(car.vehicle_inspection)" />
              <DetailItem title="保険" :value="displayValue(car.insurance)" />
              <DetailItem title="排気量" :value="car.displacement ? `${formatNumber(car.displacement)}cc` : '-'" />
              <DetailItem title="走行距離" :value="car.mileage ? `${formatNumber(car.mileage)}km` : '-'" />
              <DetailItem title="ミッション" :value="displayValue(car.mission)" />
            </div>

            <div class="mt-2 md:mt-0 flex flex-col gap-2 text-xs font-bold">
              <DetailItem title="モデル" :value="displayValue(car.model)" />
              <DetailItem title="ハンドル" :value="displayValue(car.handle)" />
              <DetailItem title="エンジン" :value="displayValue(car.engine_type)" />
              <DetailItem title="駆動方式" :value="displayValue(car.driving_system)" />
              <DetailItem title="修復歴" :value="displayValue(car.fix_history)" />
              <DetailItem title="整備" :value="displayValue(car.maintenance)" />
            </div>
          </div>
        </div>
        <UButton
          :to="{ name: 'cars-id', params: { id: car.id } }"
          class="mt-2 px-4 self-end font-bold"
          size="xl"
          >詳細を見る</UButton
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  car: UsedCars;
}>();

// Helper function to handle null, undefined, and empty strings
const displayValue = (value: string | null | undefined): string => {
  if (value === null || value === undefined) return '-';
  const trimmed = value.toString().trim();
  return trimmed === '' ? '-' : trimmed;
};

// Add formatNumber function
const formatNumber = (value: number | string): string => {
  if (typeof value === 'string') {
    value = parseFloat(value);
  }
  if (isNaN(value)) return '0';
  return value.toLocaleString();
};

// Helper function to format dates
const formatDate = (dateString: string | Date | null | undefined): string => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return '-';
  }
};

// const visibleRef = ref(false);
// const indexRef = ref(0);

// Utility function to validate image URLs
function isValidImageUrl(url: string): boolean {
  if (!url || typeof url !== "string") return false;

  // Remove whitespace
  url = url.trim();

  // Check if empty after trimming
  if (!url) return false;

  // Check for basic URL structure
  // Allow both http/https and protocol-relative URLs (//)
  const urlPattern = /^(https?:\/\/|\/\/)[^\s<>"{}|\\^`[\]]+$/i;
  if (!urlPattern.test(url)) return false;

  // Filter out known problematic patterns
  const problematicPatterns = [
    /animation_M\.gif/i,
    /placeholder/i,
    /no-image/i,
    /noimage/i,
    /loading/i,
    /spinner/i,
    /blank\.(jpg|png|gif)/i,
  ];

  for (const pattern of problematicPatterns) {
    if (pattern.test(url)) return false;
  }

  return true;
}

const images = computed(() => {
  const imageUrls = props.car.image?.split(",") ?? [];
  return imageUrls
    .map((url) => url.trim())
    .filter((url) => isValidImageUrl(url));
});

const carousel = useTemplateRef("carousel");
const activeIndex = ref(0);

function onClickPrev() {
  activeIndex.value--;
}
function onClickNext() {
  activeIndex.value++;
}
function onSelect(index: number) {
  activeIndex.value = index;
}

function select(index: number) {
  activeIndex.value = index;

  carousel.value?.emblaApi?.scrollTo(index);

  // indexRef.value = index;
}
</script>

<style></style>

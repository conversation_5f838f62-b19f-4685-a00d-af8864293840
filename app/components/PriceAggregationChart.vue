<template>
  <!-- Price Range Graph -->
  <div id="price-range-graph" class="max-w-[1200px] mx-auto">
    <div class="grid grid-cols-2 md:flex gap-2 mb-4">
      <p>価格帯グラフ</p>
      <!-- <UInputNumber
        size="xs"
        v-model="incrementManYen"
        :step="50"
        :min="50"
        :max="1000"
        class="max-w-24"
      ></UInputNumber> -->
      <!-- Increment control -->
      <USelect
        v-model="incrementManYen"
        variant="outline"
        class="hidden md:block text-primary"
        :items="[500, 1000]"
        >{{ incrementManYen }}
        <span class="text-gray-600 text-xs break-keep">万円刻み (横棒グラフ)</span>
      </USelect>
      <!-- Toggle for logarithmic scale -->
      <UToggle
        v-model="useLogScale"
        class="hidden md:flex items-center gap-2"
      >
        <span class="text-gray-600 text-xs">対数スケール</span>
      </UToggle>
      <!-- Increment control -->
      <USelect
        v-model="incrementManYen"
        variant="outline"
        class="md:hidden text-primary"
        :items="[500, 1000]"
        size="sm"
        >{{ incrementManYen }}
        <span class="text-gray-600 text-xs break-keep">万円刻み</span>
      </USelect>
      <!-- Toggle for logarithmic scale (mobile) -->
      <UToggle
        v-model="useLogScale"
        class="md:hidden flex items-center gap-2"
        size="sm"
      >
        <span class="text-gray-600 text-xs">対数スケール</span>
      </UToggle>
    </div>
    <VueApexCharts
      :options="chartOptions"
      :series="chartSeries"
      :height="dynamicHeight"
      type="bar"
    />
  </div>
</template>

<script lang="ts" setup>
// @ts-ignore
import VueApexCharts from 'vue3-apexcharts';

const props = defineProps<{ cars: UsedCars[] }>();

const startIncrement = 50;
const endIncrement = 1000;
const incrementSteps = 50;

const generatedIncrements = () => {
  const increments = [];
  for (let i = startIncrement; i <= endIncrement; i += incrementSteps) {
    increments.push(i);
  }
  return increments;
};

// Increments in 万円. 500 = 1-500万円, 500~1000万円 ... 950~1億円, 1億1円~
const incrementManYen = useCookie("incrementManYen", { default: () => 500 });
const showAllYAxisLabels = ref(true);
const useLogScale = useCookie("useLogScale", { default: () => false });

// Range Max Selection - dynamic based on actual data
const rangeMaxSelected = computed(() => {
  // Calculate actual maximum price from data
  const actualMax = Math.max(
    ...props.cars.map((car) => Number(car.price || 0))
  );
  
  // Round up to nearest 500 and add some buffer
  const roundedActualMax = Math.ceil(actualMax / 500) * 500;
  
  // Ensure minimum range based on increment
  const minRange = incrementManYen.value === 500 ? 10000 : 20000;
  
  // Return the larger of actual max or minimum range
  return Math.max(minRange, roundedActualMax + 1000); // Add 1000万円 buffer
});

const generatedRangeMax = () => {
  // Calculate actual maximum price from data
  const actualMax = Math.max(
    ...props.cars.map((car) => Number(car.price || 0))
  );
  const roundedActualMax = Math.ceil(actualMax / 500) * 500; // Round up to nearest 500

  const ranges = [1000, 2000, 3000, 5000, 8000, 10000];

  // Add the actual max if it's not already in our preset ranges
  if (!ranges.includes(roundedActualMax) && roundedActualMax > 1000) {
    ranges.push(roundedActualMax);
    ranges.sort((a, b) => a - b);
  }

  return ranges;
};

// Create computed property to generate data based on incrementManYen and raw cars data
const computedPriceAggregationData = computed(() => {
  const increment = incrementManYen.value;
  const maxRange = rangeMaxSelected.value;

  // Pre-generate all possible price ranges including empty ones
  const allRanges: string[] = [];

  // Start with 応談
  allRanges.push("応談");

  // Generate ranges up to user-selected maximum
  for (let start = 1; start <= maxRange; start += increment) {
    const end = Math.min(start + increment - 1, maxRange);
    if (end >= maxRange) {
      // For ranges that reach the maximum, use proper format
      if (maxRange === 10000) {
        allRanges.push(`${start}~1億円`);
      } else if (maxRange === 20000) {
        allRanges.push(`${start}~2億円`);
      } else if (maxRange > 10000) {
        allRanges.push(
          `${start}~${Math.floor(maxRange / 10000)}億${
            maxRange % 10000 ? (maxRange % 10000) + "万" : ""
          }円`
        );
      } else {
        allRanges.push(`${start}~${maxRange}万円`);
      }
      break;
    } else {
      allRanges.push(`${start}~${end}万円`);
    }
  }

  // Add the final range for cars over the maximum range
  if (maxRange === 10000) {
    allRanges.push("1億1円~");
  } else if (maxRange === 20000) {
    allRanges.push("2億1円~");
  } else if (maxRange > 10000) {
    allRanges.push(
      `${Math.floor(maxRange / 10000)}億${
        maxRange % 10000 ? (maxRange % 10000) + "万" : ""
      }1円~`
    );
  } else {
    allRanges.push(`${maxRange}万1円~`);
  }

  // Initialize all ranges with 0 count
  const priceRanges = new Map<string, number>();
  allRanges.forEach((range) => {
    priceRanges.set(range, 0);
  });

  // Process each car and increment the appropriate range
  props.cars.forEach((car) => {
    const price = car.price;

    // Handle null, 0, or 0.00 as 応談 (negotiable)
    if (!price || price === 0 || price === 0.0 || Number(price) === 0) {
      priceRanges.set("応談", (priceRanges.get("応談") || 0) + 1);
      return;
    }

    // Determine which price range this car falls into
    let rangeKey: string;

    if (price <= increment) {
      rangeKey = `1~${increment}万円`;
    } else if (price > maxRange) {
      // Over maximum range
      if (maxRange === 10000) {
        rangeKey = "1億1円~";
      } else if (maxRange === 20000) {
        rangeKey = "2億1円~";
      } else if (maxRange > 10000) {
        rangeKey = `${Math.floor(maxRange / 10000)}億${
          maxRange % 10000 ? (maxRange % 10000) + "万" : ""
        }1円~`;
      } else {
        rangeKey = `${maxRange}万1円~`;
      }
    } else {
      // Calculate range
      const lowerBound = Math.floor((price - 1) / increment) * increment + 1;
      const upperBound = Math.min(lowerBound + increment - 1, maxRange);

      if (upperBound >= maxRange) {
        // For ranges that reach the maximum, use proper format
        if (maxRange === 10000) {
          rangeKey = `${lowerBound}~1億円`;
        } else if (maxRange === 20000) {
          rangeKey = `${lowerBound}~2億円`;
        } else if (maxRange > 10000) {
          rangeKey = `${lowerBound}~${Math.floor(maxRange / 10000)}億${
            maxRange % 10000 ? (maxRange % 10000) + "万" : ""
          }円`;
        } else {
          rangeKey = `${lowerBound}~${maxRange}万円`;
        }
      } else {
        rangeKey = `${lowerBound}~${upperBound}万円`;
      }
    }

    // Only increment if the range exists in our map
    if (priceRanges.has(rangeKey)) {
      priceRanges.set(rangeKey, (priceRanges.get(rangeKey) || 0) + 1);
    }
  });

  // Debug: log the ranges being generated
  console.log('Generated ranges:', allRanges);
  console.log('Price range map:', Array.from(priceRanges.entries()));

  // Create the data array
  let dataArray = allRanges
    .map((range) => ({
      priceRange: range,
      cars: priceRanges.get(range) || 0,
    }));

  // Filter based on showAllYAxisLabels toggle
  if (!showAllYAxisLabels.value) {
    dataArray = dataArray.filter(
      (item) => item.cars > 0 || item.priceRange === "応談"
    );
  }

  return dataArray;
});

const RevenueCategories = computed(() => ({
  cars: {
    name: "車",
    color: "var(--ui-primary)",
  },
}));

const dynamicHeight = computed(() => {
  const ratio = rangeMaxSelected.value / incrementManYen.value;
  const baselineRatio = 20;
  const baselineHeight = 400;
  const calculatedHeight = Math.round((ratio / baselineRatio) * baselineHeight);
  return Math.max(400, Math.min(800, calculatedHeight));
});

// Helper function for logarithmic transformation
const transformValue = (value: number): number => {
  if (!useLogScale.value) return value;
  // Use log(value + 1) to handle zero values gracefully
  return value > 0 ? Math.log10(value + 1) : 0;
};



// ApexCharts configuration
const chartOptions = computed(() => {
  const data = computedPriceAggregationData.value;
  const rawValues = data.map(item => item.cars);
  const transformedValues = rawValues.map(transformValue);
  const maxValue = useLogScale.value ? Math.max(...transformedValues) : Math.max(...rawValues);
  
  // カスタムティックを生成
  const generateCustomTicks = (max: number, isLogScale: boolean) => {
    if (isLogScale) {
      // 対数スケール用のティック（変換後の値）
      const ticks = [0]; // log(1) = 0

      // 対数スケールでは 1, 10, 100, 1000, 10000 に対応する変換値を使用
      const logValues = [
        { original: 1, transformed: Math.log10(2) },      // log(1+1)
        { original: 10, transformed: Math.log10(11) },    // log(10+1)
        { original: 100, transformed: Math.log10(101) },  // log(100+1)
        { original: 1000, transformed: Math.log10(1001) }, // log(1000+1)
        { original: 10000, transformed: Math.log10(10001) } // log(10000+1)
      ];

      logValues.forEach(({ transformed }) => {
        if (transformed <= max) {
          ticks.push(Math.round(transformed * 100) / 100); // 小数点2桁で丸める
        }
      });

      // 最大値も追加
      if (max > 0 && !ticks.includes(max)) {
        ticks.push(Math.round(max * 100) / 100);
      }

      return ticks;
    } else {
      // 線形スケール用のティック（元の実装）
      const ticks = [0];

      if (max >= 10) ticks.push(10);
      if (max >= 100) ticks.push(100);
      if (max >= 1000) ticks.push(1000);

      if (max > 1000) {
        const roundedMax = Math.ceil(max / 1000) * 1000;
        if (!ticks.includes(roundedMax)) {
          ticks.push(roundedMax);
        }
      } else if (max > 100) {
        const roundedMax = Math.ceil(max / 100) * 100;
        if (!ticks.includes(roundedMax)) {
          ticks.push(roundedMax);
        }
      } else if (max > 10) {
        const roundedMax = Math.ceil(max / 10) * 10;
        if (!ticks.includes(roundedMax)) {
          ticks.push(roundedMax);
        }
      } else {
        ticks.push(max);
      }

      return ticks;
    }
  };
  
  const customTicks = generateCustomTicks(maxValue, useLogScale.value);
  
  return {
    chart: {
      type: 'bar',
      toolbar: {
        show: false
      },
      width: '100%',
      maxWidth: 1200
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '80%',
        distributed: false,
        // バーの幅を調整
        columnWidth: '70%',
        // 最小バー幅を設定（より大きく）
        minBarLength: 10,
        // バーの角を丸くする
        borderRadius: 2
      }
    },
    colors: ['var(--ui-primary)'],
    dataLabels: {
      enabled: false,
      formatter: function (val: number) {
        return val > 0 ? val + '台' : '';
      },
      style: {
        fontSize: '10px',
        colors: ['#fff']
      },
      // 小さい値でも表示されるようにする
      offsetX: 0,
      offsetY: 0
    },
    xaxis: {
      categories: data.map(item => item.priceRange),
      labels: {
        style: {
          fontSize: '12px'
        },
        formatter: function(val: number) {
          if (!useLogScale.value) return val.toString();

          // 対数スケールの場合、元の値に対応するラベルを表示
          const tickLabels: { [key: number]: string } = {};

          // 主要な値のラベルを設定
          tickLabels[0] = '0';
          tickLabels[Math.round(Math.log10(2) * 100) / 100] = '1';
          tickLabels[Math.round(Math.log10(11) * 100) / 100] = '10';
          tickLabels[Math.round(Math.log10(101) * 100) / 100] = '100';
          tickLabels[Math.round(Math.log10(1001) * 100) / 100] = '1K';
          tickLabels[Math.round(Math.log10(10001) * 100) / 100] = '10K';

          const roundedVal = Math.round(val * 100) / 100;
          return tickLabels[roundedVal] || val.toString();
        }
      },
      min: 0,
      max: Math.max(useLogScale.value ? 2 : 100, Math.ceil(maxValue * 1.2)),
      tickAmount: customTicks.length - 1,
      tickPlacement: 'between'
    },
    yaxis: {
      labels: {
        style: {
          fontSize: '12px'
        }
      }
    },
    grid: {
      show: false
    },
    tooltip: {
      y: {
        formatter: function (_val: number, opts: any) {
          // Show original value in tooltip, not transformed value
          const dataIndex = opts.dataPointIndex;
          const originalValue = data[dataIndex]?.cars || 0;
          return originalValue + ' 台' + (useLogScale.value ? ' (対数表示)' : '');
        }
      }
    },
    responsive: [{
      breakpoint: 1200,
      options: {
        chart: {
          width: '100%'
        }
      }
    }]
  };
});

const chartSeries = computed(() => [{
  name: '車',
  data: computedPriceAggregationData.value.map(item => transformValue(item.cars))
}]);


</script>

<style></style>

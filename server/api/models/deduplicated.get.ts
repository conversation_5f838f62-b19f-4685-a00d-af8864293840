export default defineCachedEventHandler(async (event) => {
  const query = getQuery(event)
  const config = useRuntimeConfig()

  try {
    const directusUrl = config.public.directus.url
    const authToken = config.directusAppToken

    if (!authToken) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Directus app token not configured',
      })
    }

    // Get blacklist data
    const [blacklistedMakersResponse, blacklistedModelsResponse] = await Promise.all([
      $fetch<{ data: Array<{ value: string }> }>(`${directusUrl}/items/blacklist?filter[type][_eq]=maker&fields=value`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      }),
      $fetch<{ data: Array<{ value: string }> }>(`${directusUrl}/items/blacklist?filter[type][_eq]=model&fields=value`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      })
    ])

    const blacklistedMakerKeywords = blacklistedMakersResponse?.data?.filter(item => item.value).map(item => item.value.toLowerCase()) || []
    const blacklistedModelKeywords = blacklistedModelsResponse?.data?.filter(item => item.value).map(item => item.value.toLowerCase()) || []

    // Build filters for the query
    const filters = []

    // Add existing filters from query if any
    if (query.filter) {
      try {
        const existingFilter = JSON.parse(query.filter as string)
        filters.push(existingFilter)
      } catch (error) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid filter parameter: malformed JSON',
        })
      }
    }

    // Add blacklist filters
    if (blacklistedMakerKeywords.length > 0) {
      filters.push({
        maker: {
          _nin: blacklistedMakerKeywords,
        },
      })
    }

    if (blacklistedModelKeywords.length > 0) {
      filters.push({
        model: {
          _nin: blacklistedModelKeywords,
        },
      })
    }

    // Add default filters to match cars page behavior
    // Year range: 1886-2005
    filters.push({
      model_year: {
        _gte: "1886-01-01",
        _lte: "2005-12-31",
      },
    })

    // Price range: 0-10000
    filters.push({
      price: {
        _gte: 0,
        _lte: 10000,
      },
    })

    // Mileage range: 0-1000000
    filters.push({
      mileage: {
        _gte: 0,
        _lte: 1000000,
      },
    })

    // Since we can't deduplicate by source_url directly in the aggregation query,
    // we need to fetch all records first, then deduplicate, then aggregate
    const directusParams = new URLSearchParams()
    
    // Add the filter
    const finalFilter = filters.length > 0 ? { _and: filters } : {}
    if (Object.keys(finalFilter).length > 0) {
      directusParams.set('filter', JSON.stringify(finalFilter))
    }

    // We need all records to deduplicate by source_url
    directusParams.set('limit', '-1')
    directusParams.set('fields', 'maker,model,source_url,scraped_at')

    // Fetch all matching records
    const response = await $fetch<{ data: Array<{ maker: string, model: string, source_url: string, scraped_at: string }> }>(`${directusUrl}/items/used_cars?${directusParams}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response?.data) {
      return []
    }

    // Deduplicate by source_url, keeping the latest scraped_at
    const urlMap = new Map()
    response.data.forEach((car) => {
      if (!car.source_url) return
      
      const existing = urlMap.get(car.source_url)
      if (!existing || (car.scraped_at && car.scraped_at > existing.scraped_at)) {
        urlMap.set(car.source_url, car)
      }
    })

    const deduplicatedCars = Array.from(urlMap.values())

    // Now aggregate by maker and model
    const aggregatedModels = new Map()
    deduplicatedCars.forEach((car) => {
      const key = `${car.maker}|${car.model}`
      const existing = aggregatedModels.get(key)
      
      if (existing) {
        existing.count++
      } else {
        aggregatedModels.set(key, {
          maker: car.maker,
          model: car.model,
          count: 1
        })
      }
    })

    // Convert to array and sort by model name
    const result = Array.from(aggregatedModels.values())
      .sort((a, b) => a.model.localeCompare(b.model))

    return result
  } catch (error) {
    console.error('Error fetching deduplicated models:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch model data',
    })
  }
}, {
  maxAge: 2,
  getKey: (event) => {
    const query = getQuery(event)
    return JSON.stringify(query)
  }
})
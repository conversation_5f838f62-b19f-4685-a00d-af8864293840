export default defineCachedEventHandler(async (event) => {
  const query = getQuery(event)
  const config = useRuntimeConfig()

  try {
    const directusUrl = config.public.directus.url
    const authToken = config.directusAppToken

    if (!authToken) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Directus app token not configured',
      })
    }

    // Get blacklist keywords first
    const blacklistResponse = await $fetch<{ data: Array<{ value: string }> }>(`${directusUrl}/items/blacklist?fields=value`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    })

    const blacklistKeywords = blacklistResponse?.data?.filter(item => item.value).map(item => item.value.toLowerCase()) || []

    // Build Directus query parameters by proxying client request
    const directusParams = new URLSearchParams()
    
    // Copy all client parameters
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          directusParams.append(key, value.join(','))
        } else {
          directusParams.append(key, String(value))
        }
      }
    })

    // Add blacklist filtering to the Directus query
    let existingFilter = {}
    if (query.filter) {
      try {
        existingFilter = JSON.parse(query.filter as string)
      } catch (error) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid filter parameter: malformed JSON',
        })
      }
    }
    
    // Add blacklist exclusions to filter
    if (blacklistKeywords.length > 0) {
      const blacklistFilters = blacklistKeywords.map(keyword => ({
        car_name: { _ncontains: keyword }
      }))
      
      existingFilter = {
        _and: [
          existingFilter,
          { _and: blacklistFilters }
        ]
      }
      
      try {
        directusParams.set('filter', JSON.stringify(existingFilter))
      } catch (error) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to serialize filter parameters',
        })
      }
    }

    // Proxy request to Directus
    const response = await $fetch<{ data: UsedCars[] }>(`${directusUrl}/items/used_cars?${directusParams}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response?.data) {
      return []
    }

    // Post-process: Deduplicate by URL, keeping the latest scraped_at
    const urlMap = new Map()
    response.data.forEach((car) => {
      if (!car.source_url) return
      
      const existing = urlMap.get(car.source_url)
      if (!existing || (car.scraped_at && car.scraped_at > existing.scraped_at)) {
        urlMap.set(car.source_url, car)
      }
    })

    return Array.from(urlMap.values())
  } catch (error) {
    console.error('Error fetching deduplicated cars:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch car data',
    })
  }
},{
  maxAge: 2,
  getKey: (event) => {
    const query = getQuery(event)
    return JSON.stringify(query)
  },
  shouldInvalidateCache: (event) => {
    const query = getQuery(event)
    return query.invalidate === 'true'
  }
})
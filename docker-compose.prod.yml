services:
  # Nuxt Application - Built locally from source
  nuxt-app:
    env_file:
      - .env
    build:
      context: .
      dockerfile: Dockerfile
    container_name: usedcar_nuxt_app
    ports:
      - "${APP_PORT:-3001}:3000"
    environment:
      - NODE_ENV
      - DB_HOST
      - DB_PORT
      - DB_NAME
      - DB_USER
      - DB_PASSWORD
      - DB_SSL
      - OUTPUT_MODE
      - BATCH_SIZE
      - BLACKLIST_ENABLED
      - DIRECTUS_KEY
      - DIRECTUS_SECRET
      - DIRECTUS_ADMIN_EMAIL
      - DIRECTUS_ADMIN_PASSWORD
      - DIRECTUS_PUBLIC_URL
      - DIRECTUS_APP_TOKEN
    volumes:
      - scraper_output:/app/scraper/output
      - scraper_output_merge:/app/scraper/output_merge
    depends_on:
      db-init:
        condition: service_completed_successfully
    networks:
      - usedcar_network
    restart: unless-stopped

  # CockroachDB Database
  cockroach_db:
    image: cockroachdb/cockroach:v24.1.22
    container_name: usedcar_cockroachdb
    ports:
      - "${DB_EXTERNAL_PORT:-26258}:26257"
      - "${DB_UI_PORT:-8081}:8080"
    command: start-single-node --insecure
    volumes:
      - cockroach_data:/cockroach/cockroach-data
    environment:
      - COCKROACH_DATABASE=usedcarsales
      - COCKROACH_USER=root
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health?ready=1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    networks:
      - usedcar_network

  # Database Initialization
  db-init:
    image: postgres:15.14-alpine
    container_name: usedcar_db_init
    volumes:
      - ./scraper/database/init:/init-scripts
    environment:
      - PGPASSWORD=""
    command: >
      sh -c "
        echo 'Waiting for CockroachDB to be ready...' &&
        sleep 15 &&
        echo 'Running complete database initialization...' &&
        psql -h cockroach_db -p 26257 -U root -d postgres -f /init-scripts/01-init.sql &&
        echo 'Database initialization completed successfully'
      "
    depends_on:
      cockroach_db:
        condition: service_healthy
    networks:
      - usedcar_network
    restart: "no"

  # Directus Admin Interface
  directus:
    image: directus/directus:11.11.0
    container_name: usedcar_directus
    ports:
      - "${DIRECTUS_PORT:-8056}:8055"
    volumes:
      - directus_uploads:/directus/uploads
      - directus_extensions:/directus/extensions
      - directus_database:/directus/database
    environment:
      # Database Configuration
      DB_CLIENT: cockroachdb
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_SSL: ${DB_SSL}

      # Directus Configuration
      KEY: ${DIRECTUS_KEY}
      SECRET: ${DIRECTUS_SECRET}

      # Admin User
      ADMIN_EMAIL: ${DIRECTUS_ADMIN_EMAIL}
      ADMIN_PASSWORD: ${DIRECTUS_ADMIN_PASSWORD}

      # Public URL
      PUBLIC_URL: ${DIRECTUS_PUBLIC_URL}

      # CORS Configuration
      CORS_ENABLED: true
      CORS_ORIGIN: "http://************:3001,http://localhost:3001"
      CORS_METHODS: "GET,POST,PATCH,DELETE,OPTIONS"
      CORS_ALLOWED_HEADERS: "Content-Type,Authorization"

      # Cache Configuration
      CACHE_ENABLED: false
      CACHE_STORE: redis
      CACHE_TTL: 30m
      CACHE_AUTO_PURGE: true
      CACHE_SCHEMA: true
      
      # Redis Configuration
      REDIS: redis://redis:6379

      # Force fresh installation
      RESET_ADMIN: true
    depends_on:
      db-init:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    networks:
      - usedcar_network
    restart: unless-stopped

  # Redis Cache (optional, for Directus caching)
  redis:
    image: redis:7.4.5-alpine
    container_name: ${COMPOSE_PROJECT_NAME:-usedcar}_redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - usedcar_network
    restart: unless-stopped

volumes:
  cockroach_data:
    driver: local
  redis_data:
    driver: local
  scraper_output:
    driver: local
  scraper_output_merge:
    driver: local
  directus_uploads:
    driver: local
  directus_extensions:
    driver: local
  directus_database:
    driver: local

networks:
  usedcar_network:
    driver: bridge